import { NextResponse } from 'next/server';
import { getPositionTypes } from '@/lib/database';

export async function GET() {
  try {
    const positionTypes = getPositionTypes();
    return NextResponse.json({ success: true, data: positionTypes });
  } catch (error) {
    console.error('获取岗位类型失败:', error);
    return NextResponse.json(
      { success: false, error: '获取岗位类型失败' },
      { status: 500 }
    );
  }
}
