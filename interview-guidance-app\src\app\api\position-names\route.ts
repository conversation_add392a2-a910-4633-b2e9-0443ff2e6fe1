import { NextRequest, NextResponse } from 'next/server';
import { getPositionNames } from '@/lib/database';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const positionType = searchParams.get('positionType');
    
    if (!positionType) {
      return NextResponse.json(
        { success: false, error: '缺少岗位类型参数' },
        { status: 400 }
      );
    }
    
    const positionNames = getPositionNames(positionType);
    return NextResponse.json({ success: true, data: positionNames });
  } catch (error) {
    console.error('获取岗位名称失败:', error);
    return NextResponse.json(
      { success: false, error: '获取岗位名称失败' },
      { status: 500 }
    );
  }
}
