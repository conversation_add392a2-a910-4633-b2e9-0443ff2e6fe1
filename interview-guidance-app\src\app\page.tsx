'use client';

import { useState, useEffect } from 'react';
import { AssessmentItem } from '@/lib/database';

export default function Home() {
  const [positionTypes, setPositionTypes] = useState<string[]>([]);
  const [positionNames, setPositionNames] = useState<string[]>([]);
  const [assessmentItems, setAssessmentItems] = useState<string[]>([]);
  const [selectedPositionType, setSelectedPositionType] = useState<string>('');
  const [selectedPositionName, setSelectedPositionName] = useState<string>('');
  const [selectedAssessmentItem, setSelectedAssessmentItem] = useState<string>('');
  const [assessmentDetails, setAssessmentDetails] = useState<AssessmentItem | null>(null);
  const [loading, setLoading] = useState<boolean>(false);

  // 获取岗位类型
  useEffect(() => {
    const fetchPositionTypes = async () => {
      try {
        const response = await fetch('/api/position-types');
        const result = await response.json();
        if (result.success) {
          setPositionTypes(result.data);
        }
      } catch (error) {
        console.error('获取岗位类型失败:', error);
      }
    };

    fetchPositionTypes();
  }, []);

  // 当选择岗位类型时，获取对应的岗位名称
  useEffect(() => {
    if (selectedPositionType) {
      const fetchPositionNames = async () => {
        try {
          const response = await fetch(`/api/position-names?positionType=${encodeURIComponent(selectedPositionType)}`);
          const result = await response.json();
          if (result.success) {
            setPositionNames(result.data);
            setSelectedPositionName('');
            setAssessmentItems([]);
            setSelectedAssessmentItem('');
            setAssessmentDetails(null);
          }
        } catch (error) {
          console.error('获取岗位名称失败:', error);
        }
      };

      fetchPositionNames();
    } else {
      setPositionNames([]);
      setSelectedPositionName('');
      setAssessmentItems([]);
      setSelectedAssessmentItem('');
      setAssessmentDetails(null);
    }
  }, [selectedPositionType]);

  // 当选择岗位名称时，获取对应的考察项
  useEffect(() => {
    if (selectedPositionType && selectedPositionName) {
      const fetchAssessmentItems = async () => {
        try {
          const response = await fetch(`/api/assessment-items?positionType=${encodeURIComponent(selectedPositionType)}&positionName=${encodeURIComponent(selectedPositionName)}`);
          const result = await response.json();
          if (result.success) {
            setAssessmentItems(result.data);
            setSelectedAssessmentItem('');
            setAssessmentDetails(null);
          }
        } catch (error) {
          console.error('获取考察项失败:', error);
        }
      };

      fetchAssessmentItems();
    } else {
      setAssessmentItems([]);
      setSelectedAssessmentItem('');
      setAssessmentDetails(null);
    }
  }, [selectedPositionType, selectedPositionName]);

  // 获取考察项详情
  const fetchAssessmentDetails = async (assessmentItem: string) => {
    setLoading(true);
    try {
      const response = await fetch(`/api/assessment-details?assessmentItem=${encodeURIComponent(assessmentItem)}`);
      const result = await response.json();
      if (result.success) {
        setAssessmentDetails(result.data);
      }
    } catch (error) {
      console.error('获取考察项详情失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAssessmentItemClick = (item: string) => {
    setSelectedAssessmentItem(item);
    fetchAssessmentDetails(item);
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">面试指导系统</h1>
          <p className="text-lg text-gray-600">基于岗位人才画像的面试评估工具</p>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* 岗位类型选择 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                选择岗位类型
              </label>
              <select
                value={selectedPositionType}
                onChange={(e) => setSelectedPositionType(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">请选择岗位类型</option>
                {positionTypes.map((type) => (
                  <option key={type} value={type}>
                    {type}
                  </option>
                ))}
              </select>
            </div>

            {/* 岗位名称选择 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                选择岗位名称
              </label>
              <select
                value={selectedPositionName}
                onChange={(e) => setSelectedPositionName(e.target.value)}
                disabled={!selectedPositionType}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100 disabled:cursor-not-allowed"
              >
                <option value="">请选择岗位名称</option>
                {positionNames.map((name) => (
                  <option key={name} value={name}>
                    {name}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* 考察项展示 */}
        {assessmentItems.length > 0 && (
          <div className="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">考察项列表</h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
              {assessmentItems.map((item) => (
                <button
                  key={item}
                  onClick={() => handleAssessmentItemClick(item)}
                  className={`p-3 text-left rounded-lg border transition-colors ${
                    selectedAssessmentItem === item
                      ? 'bg-blue-50 border-blue-500 text-blue-700'
                      : 'bg-gray-50 border-gray-200 hover:bg-gray-100 text-gray-700'
                  }`}
                >
                  {item}
                </button>
              ))}
            </div>
          </div>
        )}
        {/* 考察项详情展示 */}
        {loading && (
          <div className="bg-white rounded-lg shadow-md p-6 text-center">
            <div className="text-gray-600">加载中...</div>
          </div>
        )}

        {assessmentDetails && !loading && (
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-2xl font-semibold text-gray-900 mb-6">
              考察项详情：{assessmentDetails.assessment_item}
            </h2>

            {/* 精准提问话术 */}
            <div className="mb-8">
              <h3 className="text-lg font-semibold text-gray-800 mb-4">精准提问话术</h3>
              <div className="space-y-4">
                {assessmentDetails.question_1 && (
                  <div className="bg-blue-50 p-4 rounded-lg">
                    <div className="font-medium text-blue-800 mb-2">话术1</div>
                    <div className="text-gray-700">{assessmentDetails.question_1}</div>
                  </div>
                )}
                {assessmentDetails.question_2 && (
                  <div className="bg-green-50 p-4 rounded-lg">
                    <div className="font-medium text-green-800 mb-2">话术2</div>
                    <div className="text-gray-700">{assessmentDetails.question_2}</div>
                  </div>
                )}
                {assessmentDetails.question_3 && (
                  <div className="bg-purple-50 p-4 rounded-lg">
                    <div className="font-medium text-purple-800 mb-2">话术3</div>
                    <div className="text-gray-700">{assessmentDetails.question_3}</div>
                  </div>
                )}
              </div>
            </div>

            {/* 追问参考 */}
            <div className="mb-8">
              <h3 className="text-lg font-semibold text-gray-800 mb-4">追问参考</h3>
              <div className="space-y-4">
                {assessmentDetails.follow_up_1 && (
                  <div className="bg-yellow-50 p-4 rounded-lg">
                    <div className="font-medium text-yellow-800 mb-2">话术1追问参考</div>
                    <div className="text-gray-700">{assessmentDetails.follow_up_1}</div>
                  </div>
                )}
                {assessmentDetails.follow_up_2 && (
                  <div className="bg-orange-50 p-4 rounded-lg">
                    <div className="font-medium text-orange-800 mb-2">话术2追问参考</div>
                    <div className="text-gray-700">{assessmentDetails.follow_up_2}</div>
                  </div>
                )}
                {assessmentDetails.follow_up_3 && (
                  <div className="bg-red-50 p-4 rounded-lg">
                    <div className="font-medium text-red-800 mb-2">话术3追问参考</div>
                    <div className="text-gray-700">{assessmentDetails.follow_up_3}</div>
                  </div>
                )}
              </div>
            </div>

            {/* 评分标准 */}
            <div className="mb-8">
              <h3 className="text-lg font-semibold text-gray-800 mb-4">评分标准</h3>
              <div className="space-y-3">
                {assessmentDetails.score_5 && (
                  <div className="bg-green-100 p-4 rounded-lg border-l-4 border-green-500">
                    <div className="font-medium text-green-800 mb-2">5分标准</div>
                    <div className="text-gray-700 whitespace-pre-wrap">{assessmentDetails.score_5}</div>
                  </div>
                )}
                {assessmentDetails.score_4 && (
                  <div className="bg-blue-100 p-4 rounded-lg border-l-4 border-blue-500">
                    <div className="font-medium text-blue-800 mb-2">4分标准</div>
                    <div className="text-gray-700 whitespace-pre-wrap">{assessmentDetails.score_4}</div>
                  </div>
                )}
                {assessmentDetails.score_3 && (
                  <div className="bg-yellow-100 p-4 rounded-lg border-l-4 border-yellow-500">
                    <div className="font-medium text-yellow-800 mb-2">3分标准</div>
                    <div className="text-gray-700 whitespace-pre-wrap">{assessmentDetails.score_3}</div>
                  </div>
                )}
                {assessmentDetails.score_2 && (
                  <div className="bg-orange-100 p-4 rounded-lg border-l-4 border-orange-500">
                    <div className="font-medium text-orange-800 mb-2">2分标准</div>
                    <div className="text-gray-700 whitespace-pre-wrap">{assessmentDetails.score_2}</div>
                  </div>
                )}
                {assessmentDetails.score_1 && (
                  <div className="bg-red-100 p-4 rounded-lg border-l-4 border-red-500">
                    <div className="font-medium text-red-800 mb-2">1分标准</div>
                    <div className="text-gray-700 whitespace-pre-wrap">{assessmentDetails.score_1}</div>
                  </div>
                )}
              </div>
            </div>

            {/* 特殊情形处理指南 */}
            {assessmentDetails.special_handling && (
              <div className="mb-8">
                <h3 className="text-lg font-semibold text-gray-800 mb-4">特殊情形处理指南</h3>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <div className="text-gray-700 whitespace-pre-wrap">{assessmentDetails.special_handling}</div>
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
