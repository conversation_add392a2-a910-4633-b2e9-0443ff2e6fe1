#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据预处理脚本
读取Excel文件，处理"追问参考"列的数据拆分，并创建SQLite数据库
"""

import pandas as pd
import sqlite3
import re
import os

def read_excel_data(file_path):
    """读取Excel文件数据"""
    try:
        df = pd.read_excel(file_path, sheet_name='数据表')
        print(f"成功读取Excel文件，共{len(df)}行数据")
        return df
    except Exception as e:
        print(f"读取Excel文件失败: {e}")
        return None

def process_follow_up_questions(follow_up_text):
    """
    处理"追问参考"列，拆分为三个独立的问题
    """
    if pd.isna(follow_up_text) or follow_up_text == '':
        return '', '', ''
    
    # 将文本按换行符分割
    lines = str(follow_up_text).strip().split('\n')
    
    # 去除空行和双引号
    questions = []
    for line in lines:
        line = line.strip()
        if line:
            # 去除首尾的双引号
            line = line.strip('"').strip()
            if line:
                questions.append(line)
    
    # 确保有三个问题，不足的用空字符串补充
    while len(questions) < 3:
        questions.append('')
    
    return questions[0], questions[1], questions[2]

def create_database(df):
    """创建SQLite数据库"""
    # 删除已存在的数据库文件
    if os.path.exists('interview_guidance.db'):
        os.remove('interview_guidance.db')
    
    conn = sqlite3.connect('interview_guidance.db')
    cursor = conn.cursor()
    
    # 创建positions表（岗位信息表）
    cursor.execute('''
        CREATE TABLE positions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            position_type TEXT NOT NULL,
            position_name TEXT NOT NULL,
            assessment_item TEXT NOT NULL,
            UNIQUE(position_type, position_name, assessment_item)
        )
    ''')
    
    # 创建assessment_items表（考察项详情表）
    cursor.execute('''
        CREATE TABLE assessment_items (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            assessment_item TEXT NOT NULL UNIQUE,
            question_1 TEXT,
            question_2 TEXT,
            question_3 TEXT,
            score_5 TEXT,
            score_4 TEXT,
            score_3 TEXT,
            score_2 TEXT,
            score_1 TEXT,
            special_handling TEXT,
            follow_up_1 TEXT,
            follow_up_2 TEXT,
            follow_up_3 TEXT
        )
    ''')
    
    print("数据库表创建成功")
    return conn, cursor

def insert_data(df, cursor):
    """插入数据到数据库"""
    positions_data = []
    assessment_items_data = {}
    
    for index, row in df.iterrows():
        # 跳过标题行
        if index == 0:
            continue
            
        position_type = str(row['岗位类型']).strip()
        position_name = str(row['岗位名称']).strip()
        assessment_item = str(row['考察项']).strip()
        
        # 处理追问参考列
        follow_up_1, follow_up_2, follow_up_3 = process_follow_up_questions(row['追问参考'])
        
        # 收集positions数据
        positions_data.append((position_type, position_name, assessment_item))
        
        # 收集assessment_items数据（避免重复）
        if assessment_item not in assessment_items_data:
            assessment_items_data[assessment_item] = {
                'question_1': str(row['精准提问话术1']).strip() if pd.notna(row['精准提问话术1']) else '',
                'question_2': str(row['精准提问话术 2']).strip() if pd.notna(row['精准提问话术 2']) else '',
                'question_3': str(row['精准提问话术 3']).strip() if pd.notna(row['精准提问话术 3']) else '',
                'score_5': str(row['评分标准（5分）']).strip() if pd.notna(row['评分标准（5分）']) else '',
                'score_4': str(row['评分标准（4分）']).strip() if pd.notna(row['评分标准（4分）']) else '',
                'score_3': str(row['评分标准（3分）']).strip() if pd.notna(row['评分标准（3分）']) else '',
                'score_2': str(row['评分标准（2分）']).strip() if pd.notna(row['评分标准（2分）']) else '',
                'score_1': str(row['评分标准（1分）']).strip() if pd.notna(row['评分标准（1分）']) else '',
                'special_handling': str(row['特殊情形处理指南']).strip() if pd.notna(row['特殊情形处理指南']) else '',
                'follow_up_1': follow_up_1,
                'follow_up_2': follow_up_2,
                'follow_up_3': follow_up_3
            }
    
    # 插入positions数据
    cursor.executemany('''
        INSERT OR IGNORE INTO positions (position_type, position_name, assessment_item)
        VALUES (?, ?, ?)
    ''', positions_data)
    
    # 插入assessment_items数据
    for assessment_item, data in assessment_items_data.items():
        cursor.execute('''
            INSERT OR IGNORE INTO assessment_items 
            (assessment_item, question_1, question_2, question_3, score_5, score_4, score_3, score_2, score_1, special_handling, follow_up_1, follow_up_2, follow_up_3)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            assessment_item,
            data['question_1'],
            data['question_2'],
            data['question_3'],
            data['score_5'],
            data['score_4'],
            data['score_3'],
            data['score_2'],
            data['score_1'],
            data['special_handling'],
            data['follow_up_1'],
            data['follow_up_2'],
            data['follow_up_3']
        ))
    
    print(f"成功插入{len(positions_data)}条岗位数据")
    print(f"成功插入{len(assessment_items_data)}条考察项数据")

def main():
    """主函数"""
    excel_file = "十大岗位人才画像卡.xlsx"
    
    if not os.path.exists(excel_file):
        print(f"Excel文件 {excel_file} 不存在")
        return
    
    # 读取Excel数据
    df = read_excel_data(excel_file)
    if df is None:
        return
    
    # 创建数据库
    conn, cursor = create_database(df)
    
    # 插入数据
    insert_data(df, cursor)
    
    # 提交并关闭连接
    conn.commit()
    conn.close()
    
    print("数据处理完成！")
    print("数据库文件：interview_guidance.db")

if __name__ == "__main__":
    main()
