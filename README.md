# 面试指导系统

基于岗位人才画像的面试评估工具，帮助面试官进行结构化面试。

## 项目概述

本系统基于Excel文件"十大岗位人才画像卡.xlsx"中的数据，提供了一个交互式的Web界面，帮助面试官：

1. 根据岗位类型和岗位名称快速找到对应的考察项
2. 查看每个考察项的详细面试指导信息
3. 获取精准的提问话术和追问参考
4. 了解评分标准和特殊情形处理指南

## 功能特点

### 🎯 级联选择
- **岗位类型选择**：包括十大通用岗位、十大高管岗位、房地产行业、制造行业、建筑行业、外贸行业等
- **岗位名称选择**：根据选定的岗位类型动态显示对应的具体岗位
- **考察项展示**：自动显示该岗位需要考察的所有能力项

### 📋 详细信息展示
点击任意考察项后，系统会显示：

#### 精准提问话术
- **话术1**：第一个面试问题
- **话术2**：第二个面试问题  
- **话术3**：第三个面试问题

#### 追问参考（已拆分）
- **话术1追问参考**：针对第一个问题的深入追问
- **话术2追问参考**：针对第二个问题的深入追问
- **话术3追问参考**：针对第三个问题的深入追问

#### 评分标准
- **5分标准**：优秀表现的具体要求
- **4分标准**：良好表现的具体要求
- **3分标准**：合格表现的具体要求
- **2分标准**：基本表现的具体要求
- **1分标准**：不合格表现的具体要求

#### 特殊情形处理指南
针对面试过程中可能遇到的特殊情况提供处理建议

## 技术架构

### 前端
- **框架**：Next.js 15 (React 18)
- **语言**：TypeScript
- **样式**：Tailwind CSS
- **特性**：响应式设计，支持移动端访问

### 后端
- **API**：Next.js API Routes
- **数据库**：SQLite
- **数据处理**：Python pandas

### 数据库设计
- **positions表**：存储岗位类型、岗位名称、考察项的对应关系
- **assessment_items表**：存储考察项的详细信息

## 安装和运行

### 环境要求
- Node.js 18+
- Python 3.8+
- npm 或 yarn

### 安装步骤

1. **数据预处理**
```bash
# 确保Excel文件在项目根目录
python data_processor.py
```

2. **安装依赖**
```bash
cd interview-guidance-app
npm install
```

3. **启动开发服务器**
```bash
npm run dev
```

4. **访问应用**
打开浏览器访问：http://localhost:3000

## 使用指南

### 基本操作流程

1. **选择岗位类型**
   - 在第一个下拉框中选择岗位类型（如"十大通用岗位"）

2. **选择岗位名称**
   - 在第二个下拉框中选择具体岗位（如"财务经理"）
   - 系统会自动加载该岗位的所有考察项

3. **查看考察项**
   - 页面会显示该岗位需要考察的所有能力项
   - 每个考察项以按钮形式展示

4. **查看详细信息**
   - 点击任意考察项按钮
   - 系统会显示该考察项的完整面试指导信息

### 面试使用建议

1. **面试前准备**
   - 根据招聘岗位选择对应的岗位类型和名称
   - 浏览所有考察项，了解需要评估的能力维度
   - 重点关注与岗位最相关的考察项

2. **面试过程中**
   - 按照精准提问话术进行提问
   - 根据候选人回答使用追问参考深入了解
   - 参考评分标准进行客观评估

3. **面试后评估**
   - 对照评分标准给出具体分数
   - 参考特殊情形处理指南处理异常情况

## 数据说明

### 原始数据来源
- **文件名**：十大岗位人才画像卡.xlsx
- **数据量**：308行数据（包含标题行）
- **覆盖范围**：51个不同的考察项，涵盖多个行业和岗位

### 数据处理
- **追问参考拆分**：原始的"追问参考"列按换行符拆分为三个独立的列
- **数据清洗**：去除空行、双引号等格式字符
- **数据验证**：确保数据完整性和一致性

## 项目结构

```
├── data_processor.py          # 数据预处理脚本
├── interview_guidance.db      # SQLite数据库文件
├── 十大岗位人才画像卡.xlsx    # 原始Excel数据文件
├── interview-guidance-app/    # Next.js应用目录
│   ├── src/
│   │   ├── app/
│   │   │   ├── api/          # API路由
│   │   │   └── page.tsx      # 主页面
│   │   └── lib/
│   │       └── database.ts   # 数据库连接工具
│   └── package.json
└── README.md                 # 项目说明文档
```

## 开发说明

### API接口

- `GET /api/position-types` - 获取所有岗位类型
- `GET /api/position-names?positionType=xxx` - 获取指定岗位类型的岗位名称
- `GET /api/assessment-items?positionType=xxx&positionName=xxx` - 获取考察项
- `GET /api/assessment-details?assessmentItem=xxx` - 获取考察项详情

### 数据库表结构

**positions表**
- id: 主键
- position_type: 岗位类型
- position_name: 岗位名称  
- assessment_item: 考察项

**assessment_items表**
- id: 主键
- assessment_item: 考察项名称
- question_1/2/3: 精准提问话术
- score_1/2/3/4/5: 评分标准
- special_handling: 特殊情形处理指南
- follow_up_1/2/3: 追问参考（拆分后）

## 许可证

本项目仅供学习和研究使用。

## 联系方式

如有问题或建议，请联系项目开发团队。
