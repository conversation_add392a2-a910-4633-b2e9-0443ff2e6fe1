#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API测试脚本
用于测试面试指导系统的API接口功能
"""

import requests
import json
import urllib.parse

BASE_URL = "http://localhost:3000"

def test_position_types():
    """测试获取岗位类型接口"""
    print("=== 测试获取岗位类型 ===")
    try:
        response = requests.get(f"{BASE_URL}/api/position-types")
        if response.status_code == 200:
            data = response.json()
            if data['success']:
                print(f"✅ 成功获取 {len(data['data'])} 个岗位类型:")
                for i, position_type in enumerate(data['data'], 1):
                    print(f"  {i}. {position_type}")
                return data['data']
            else:
                print(f"❌ API返回错误: {data.get('error', '未知错误')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
    except Exception as e:
        print(f"❌ 请求失败: {e}")
    return []

def test_position_names(position_type):
    """测试获取岗位名称接口"""
    print(f"\n=== 测试获取岗位名称 (岗位类型: {position_type}) ===")
    try:
        encoded_type = urllib.parse.quote(position_type)
        response = requests.get(f"{BASE_URL}/api/position-names?positionType={encoded_type}")
        if response.status_code == 200:
            data = response.json()
            if data['success']:
                print(f"✅ 成功获取 {len(data['data'])} 个岗位名称:")
                for i, position_name in enumerate(data['data'], 1):
                    print(f"  {i}. {position_name}")
                return data['data']
            else:
                print(f"❌ API返回错误: {data.get('error', '未知错误')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
    except Exception as e:
        print(f"❌ 请求失败: {e}")
    return []

def test_assessment_items(position_type, position_name):
    """测试获取考察项接口"""
    print(f"\n=== 测试获取考察项 (岗位: {position_type} - {position_name}) ===")
    try:
        encoded_type = urllib.parse.quote(position_type)
        encoded_name = urllib.parse.quote(position_name)
        response = requests.get(f"{BASE_URL}/api/assessment-items?positionType={encoded_type}&positionName={encoded_name}")
        if response.status_code == 200:
            data = response.json()
            if data['success']:
                print(f"✅ 成功获取 {len(data['data'])} 个考察项:")
                for i, assessment_item in enumerate(data['data'], 1):
                    print(f"  {i}. {assessment_item}")
                return data['data']
            else:
                print(f"❌ API返回错误: {data.get('error', '未知错误')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
    except Exception as e:
        print(f"❌ 请求失败: {e}")
    return []

def test_assessment_details(assessment_item):
    """测试获取考察项详情接口"""
    print(f"\n=== 测试获取考察项详情 (考察项: {assessment_item}) ===")
    try:
        encoded_item = urllib.parse.quote(assessment_item)
        response = requests.get(f"{BASE_URL}/api/assessment-details?assessmentItem={encoded_item}")
        if response.status_code == 200:
            data = response.json()
            if data['success']:
                details = data['data']
                print("✅ 成功获取考察项详情:")
                print(f"  考察项: {details['assessment_item']}")
                
                # 显示精准提问话术
                print("\n  精准提问话术:")
                if details['question_1']:
                    print(f"    话术1: {details['question_1'][:50]}...")
                if details['question_2']:
                    print(f"    话术2: {details['question_2'][:50]}...")
                if details['question_3']:
                    print(f"    话术3: {details['question_3'][:50]}...")
                
                # 显示追问参考
                print("\n  追问参考:")
                if details['follow_up_1']:
                    print(f"    话术1追问: {details['follow_up_1']}")
                if details['follow_up_2']:
                    print(f"    话术2追问: {details['follow_up_2']}")
                if details['follow_up_3']:
                    print(f"    话术3追问: {details['follow_up_3']}")
                
                # 显示评分标准
                print("\n  评分标准:")
                for score in [5, 4, 3, 2, 1]:
                    score_key = f'score_{score}'
                    if details[score_key]:
                        print(f"    {score}分: {details[score_key][:50]}...")
                
                return details
            else:
                print(f"❌ API返回错误: {data.get('error', '未知错误')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
    except Exception as e:
        print(f"❌ 请求失败: {e}")
    return None

def main():
    """主测试函数"""
    print("🚀 开始测试面试指导系统API接口")
    print("=" * 50)
    
    # 测试获取岗位类型
    position_types = test_position_types()
    if not position_types:
        print("❌ 无法获取岗位类型，停止测试")
        return
    
    # 选择第一个岗位类型进行测试
    test_position_type = position_types[0]
    
    # 测试获取岗位名称
    position_names = test_position_names(test_position_type)
    if not position_names:
        print("❌ 无法获取岗位名称，停止测试")
        return
    
    # 选择第一个岗位名称进行测试
    test_position_name = position_names[0]
    
    # 测试获取考察项
    assessment_items = test_assessment_items(test_position_type, test_position_name)
    if not assessment_items:
        print("❌ 无法获取考察项，停止测试")
        return
    
    # 选择第一个考察项进行测试
    test_assessment_item = assessment_items[0]
    
    # 测试获取考察项详情
    details = test_assessment_details(test_assessment_item)
    
    print("\n" + "=" * 50)
    if details:
        print("🎉 所有API接口测试通过！")
        print("\n📊 测试总结:")
        print(f"  - 岗位类型数量: {len(position_types)}")
        print(f"  - 测试岗位类型: {test_position_type}")
        print(f"  - 该类型下岗位数量: {len(position_names)}")
        print(f"  - 测试岗位名称: {test_position_name}")
        print(f"  - 该岗位考察项数量: {len(assessment_items)}")
        print(f"  - 测试考察项: {test_assessment_item}")
        print(f"  - 详情数据完整性: ✅")
    else:
        print("❌ API接口测试失败")

if __name__ == "__main__":
    main()
