首先读取当前目录下的Excel表“十大岗位人才画像卡.xlsx”，这是一份用于指导面试的清单，你需要充分读取并理解这份表格。然后对表格进行改造，再建立SQLite数据库存入表格中的信息，之后利用这个数据库搭建一个Web应用。

1. 将Excel表中“追问参考”列拆分为三列：“话术1追问参考”“话术2追问参考”“话术3追问参考”
   以序号为1的数据行为例，当前“追问参考”为：

---

"拒绝诱惑时的替代方案？当时决策依据？"
"面对的最大压力是什么？如何化解？"
"得罪关键人后如何补救？关系修复证明？"
--------------------------------------

拆解为：
“话术1追问参考”："拒绝诱惑时的替代方案？当时决策依据？"
“话术2追问参考”："面对的最大压力是什么？如何化解？"
“话术3追问参考”："得罪关键人后如何补救？关系修复证明？"

2. 新建SQLite数据库，包含两个数据表用于存放改造后的Excel表内容，一个数据表包含字段“岗位类型”、“岗位名称”、“考察项”，一个数据表包含“考察项”及其它所有列
3. 做一个网页应用
   让用户可以选择“岗位类型”，再选择“岗位名称”，选择好后可以确定对应的“考察项”，将每个“考察项”中对应的信息调取出来

请按照以下步骤完成一个基于Excel数据的面试指导Web应用开发：

**第一步：读取和理解Excel文件**

1. 读取当前工作目录下的Excel文件"十大岗位人才画像卡.xlsx"
2. 分析表格结构，理解每列的含义和数据格式
3. 特别关注"追问参考"列的内容格式和分隔方式

**第二步：数据预处理**
将Excel表中的"追问参考"列按照以下规则拆分为三个独立的列：

- "话术1追问参考"：第一个问题组
- "话术2追问参考"：第二个问题组
- "话术3追问参考"：第三个问题组

拆分规则示例（基于序号1的数据行）：
原始"追问参考"内容：

```
"拒绝诱惑时的替代方案？当时决策依据？"
"面对的最大压力是什么？如何化解？"
"得罪关键人后如何补救？关系修复证明？"
```

拆分后：

- "话术1追问参考"："拒绝诱惑时的替代方案？当时决策依据？"
- "话术2追问参考"："面对的最大压力是什么？如何化解？"
- "话术3追问参考"："得罪关键人后如何补救？关系修复证明？"

**第三步：数据库设计和创建**
创建SQLite数据库，包含以下两个数据表：

表1：positions（岗位信息表）

- 字段：岗位类型、岗位名称、考察项
- 用途：存储岗位与考察项的对应关系

表2：assessment_items（考察项详情表）

- 字段：考察项、以及Excel中除"岗位类型"、"岗位名称"外的所有其他列（包括拆分后的三个追问参考列）
- 用途：存储每个考察项的详细信息

**第四步：Web应用开发**
开发一个交互式网页应用，功能要求：

1. 用户界面包含两个级联下拉选择框：
   - 第一个选择框：选择"岗位类型"
   - 第二个选择框：根据选定的岗位类型，动态显示对应的"岗位名称"选项
2. 用户选择完岗位类型和岗位名称后，系统自动显示该岗位对应的所有"考察项"
3. 用户可以点击或选择具体的"考察项"，查看该考察项的详细信息（包括拆分后的三个追问参考问题等所有相关数据）
4. 界面应该清晰易用，信息展示要有良好的可读性

**技术要求：**

- 使用现代Web框架（如React + Vite或Next.js）
- 后端使用SQLite数据库
- 确保数据的完整性和查询效率
- 在开始开发前，先分析Excel文件的具体结构和数据格式
