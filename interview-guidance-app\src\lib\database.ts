import Database from 'better-sqlite3';
import path from 'path';

// 数据库文件路径
const dbPath = path.join(process.cwd(), 'interview_guidance.db');

// 创建数据库连接
let db: Database.Database | null = null;

export function getDatabase() {
  if (!db) {
    db = new Database(dbPath);
    db.pragma('journal_mode = WAL');
  }
  return db;
}

// 数据类型定义
export interface Position {
  id: number;
  position_type: string;
  position_name: string;
  assessment_item: string;
}

export interface AssessmentItem {
  id: number;
  assessment_item: string;
  question_1: string;
  question_2: string;
  question_3: string;
  score_5: string;
  score_4: string;
  score_3: string;
  score_2: string;
  score_1: string;
  special_handling: string;
  follow_up_1: string;
  follow_up_2: string;
  follow_up_3: string;
}

// 获取所有岗位类型
export function getPositionTypes(): string[] {
  const db = getDatabase();
  const stmt = db.prepare('SELECT DISTINCT position_type FROM positions ORDER BY position_type');
  const rows = stmt.all() as { position_type: string }[];
  return rows.map(row => row.position_type);
}

// 根据岗位类型获取岗位名称
export function getPositionNames(positionType: string): string[] {
  const db = getDatabase();
  const stmt = db.prepare('SELECT DISTINCT position_name FROM positions WHERE position_type = ? ORDER BY position_name');
  const rows = stmt.all(positionType) as { position_name: string }[];
  return rows.map(row => row.position_name);
}

// 根据岗位类型和岗位名称获取考察项
export function getAssessmentItems(positionType: string, positionName: string): string[] {
  const db = getDatabase();
  const stmt = db.prepare('SELECT DISTINCT assessment_item FROM positions WHERE position_type = ? AND position_name = ? ORDER BY assessment_item');
  const rows = stmt.all(positionType, positionName) as { assessment_item: string }[];
  return rows.map(row => row.assessment_item);
}

// 根据考察项获取详细信息
export function getAssessmentItemDetails(assessmentItem: string): AssessmentItem | null {
  const db = getDatabase();
  const stmt = db.prepare('SELECT * FROM assessment_items WHERE assessment_item = ?');
  const row = stmt.get(assessmentItem) as AssessmentItem | undefined;
  return row || null;
}

// 关闭数据库连接
export function closeDatabase() {
  if (db) {
    db.close();
    db = null;
  }
}
