import { NextRequest, NextResponse } from 'next/server';
import { getAssessmentItemDetails } from '@/lib/database';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const assessmentItem = searchParams.get('assessmentItem');
    
    if (!assessmentItem) {
      return NextResponse.json(
        { success: false, error: '缺少考察项参数' },
        { status: 400 }
      );
    }
    
    const details = getAssessmentItemDetails(assessmentItem);
    
    if (!details) {
      return NextResponse.json(
        { success: false, error: '未找到考察项详情' },
        { status: 404 }
      );
    }
    
    return NextResponse.json({ success: true, data: details });
  } catch (error) {
    console.error('获取考察项详情失败:', error);
    return NextResponse.json(
      { success: false, error: '获取考察项详情失败' },
      { status: 500 }
    );
  }
}
