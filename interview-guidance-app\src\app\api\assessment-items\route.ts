import { NextRequest, NextResponse } from 'next/server';
import { getAssessmentItems } from '@/lib/database';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const positionType = searchParams.get('positionType');
    const positionName = searchParams.get('positionName');
    
    if (!positionType || !positionName) {
      return NextResponse.json(
        { success: false, error: '缺少岗位类型或岗位名称参数' },
        { status: 400 }
      );
    }
    
    const assessmentItems = getAssessmentItems(positionType, positionName);
    return NextResponse.json({ success: true, data: assessmentItems });
  } catch (error) {
    console.error('获取考察项失败:', error);
    return NextResponse.json(
      { success: false, error: '获取考察项失败' },
      { status: 500 }
    );
  }
}
